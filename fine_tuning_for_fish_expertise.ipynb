{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Jj4DWxw9eNQp", "outputId": "41e3f76d-0b53-4de4-8867-deada7b12dc8"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: openai in /usr/local/lib/python3.11/dist-packages (1.93.0)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /usr/local/lib/python3.11/dist-packages (from openai) (4.9.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/local/lib/python3.11/dist-packages (from openai) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /usr/local/lib/python3.11/dist-packages (from openai) (0.28.1)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from openai) (0.10.0)\n", "Requirement already satisfied: pydantic<3,>=1.9.0 in /usr/local/lib/python3.11/dist-packages (from openai) (2.11.7)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.11/dist-packages (from openai) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in /usr/local/lib/python3.11/dist-packages (from openai) (4.67.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.11 in /usr/local/lib/python3.11/dist-packages (from openai) (4.14.0)\n", "Requirement already satisfied: idna>=2.8 in /usr/local/lib/python3.11/dist-packages (from anyio<5,>=3.5.0->openai) (3.10)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.11/dist-packages (from httpx<1,>=0.23.0->openai) (2025.6.15)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.11/dist-packages (from httpx<1,>=0.23.0->openai) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /usr/local/lib/python3.11/dist-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai) (0.16.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.9.0->openai) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.9.0->openai) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.9.0->openai) (0.4.1)\n"]}], "source": ["!pip install openai"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "yn-YfY3ceeao", "outputId": "df386c3a-de2c-4412-9307-3426569bee9b"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["env: OPENAI_API_KEY=your-api-key\n"]}], "source": ["# Set an environment variable in Colab\n", "%env OPENAI_API_KEY= your-api-key"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "SQSzBJOleeXw", "outputId": "6698def3-7b3f-4830-8559-846290d616f0"}, "outputs": [{"data": {"text/plain": ["FileObject(id='file-MdDpDAvGrTjwUgp759njcA', bytes=64564, created_at=1751869547, filename='fish_farming_data.jsonl', object='file', purpose='fine-tune', status='processed', expires_at=None, status_details=None)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from openai import OpenAI\n", "client = OpenAI()\n", "\n", "client.files.create(\n", "  file=open(\"/content/fish_farming_data.jsonl\", \"rb\"),\n", "  purpose=\"fine-tune\"\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "h8qxSJibfQjk", "outputId": "44d8640e-1540-4401-d5d3-d32c74de65cc"}, "outputs": [{"data": {"text/plain": ["FineTuningJob(id='ftjob-UroHWzDSJKSXQpY9A4ubrcuc', created_at=1751869562, error=Error(code=None, message=None, param=None), fine_tuned_model=None, finished_at=None, hyperparameters=Hyperparameters(batch_size='auto', learning_rate_multiplier='auto', n_epochs='auto'), model='gpt-3.5-turbo-0125', object='fine_tuning.job', organization_id='org-ffnkEABLwNKMc4LtHyWC9api', result_files=[], seed=1919601165, status='validating_files', trained_tokens=None, training_file='file-MdDpDAvGrTjwUgp759njcA', validation_file=None, estimated_finish=None, integrations=[], metadata=None, method=Method(type='supervised', dpo=None, reinforcement=None, supervised=SupervisedMethod(hyperparameters=SupervisedHyperparameters(batch_size='auto', learning_rate_multiplier='auto', n_epochs='auto'))), user_provided_suffix=None, usage_metrics=None, shared_with_openai=False, eval_id=None)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from openai import OpenAI\n", "client = OpenAI()\n", "\n", "client.fine_tuning.jobs.create(\n", "  training_file=\"file-MdDpDAvGrTjwUgp759njcA\", #copy id from above\n", "  model=\"gpt-3.5-turbo\"\n", ")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "PNtyMD3gfQgv", "outputId": "233891ce-b6aa-4786-f559-96f52f3e99a9"}, "outputs": [{"data": {"text/plain": ["FineTuningJob(id='ftjob-UroHWzDSJKSXQpY9A4ubrcuc', created_at=1751869562, error=Error(code=None, message=None, param=None), fine_tuned_model=None, finished_at=None, hyperparameters=Hyperparameters(batch_size=1, learning_rate_multiplier=2.0, n_epochs=3), model='gpt-3.5-turbo-0125', object='fine_tuning.job', organization_id='org-ffnkEABLwNKMc4LtHyWC9api', result_files=[], seed=1919601165, status='running', trained_tokens=None, training_file='file-MdDpDAvGrTjwUgp759njcA', validation_file=None, estimated_finish=None, integrations=[], metadata=None, method=Method(type='supervised', dpo=None, reinforcement=None, supervised=SupervisedMethod(hyperparameters=SupervisedHyperparameters(batch_size=1, learning_rate_multiplier=2.0, n_epochs=3))), user_provided_suffix=None, usage_metrics=None, shared_with_openai=False, eval_id=None)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from openai import OpenAI\n", "client = OpenAI()\n", "\n", "# Retrieve the state of a fine-tune\n", "client.fine_tuning.jobs.retrieve(\"ftjob-UroHWzDSJKSXQpY9A4ubrcuc\") #copy id from above\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "uPCcdqD6fQex", "outputId": "ab744849-a14d-4147-c043-d355a10b0863"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ChatCompletionMessage(content='Ideal water temperature for tilapia farming is between 25°C and 30°C (77°F-86°F). Tilapia are tropical species and prefer warm water. (Add more details if needed, such as about different management practices in detail)', refusal=None, role='assistant', annotations=[], audio=None, function_call=None, tool_calls=None)\n"]}], "source": ["from openai import OpenAI\n", "client = OpenAI()  # Make sure to initialize with your API key\n", "\n", "# Prepare the system message\n", "messages = [\n", "    {\"role\": \"system\", \"content\": \"You are an assistant expert in fish farming, providing detailed and accurate information on various aspects of fish cultivation, including breeding, feeding, and pond management.\"},\n", "]\n", "\n", "# List of test questions\n", "test_questions = [\n", "    \"What is the ideal water temperature for farming tilapia? Explain to me in detail.\"\n", "]\n", "\n", "# Add each test question to the messages\n", "for question in test_questions:\n", "    messages.append({\"role\": \"user\", \"content\": question})\n", "\n", "# Make the API call\n", "response = client.chat.completions.create(\n", "    model=\"your fine tuned model\",  # Replace with your actual model\n", "    messages=messages\n", ")\n", "\n", "# Print the responses\n", "print(response.choices[0].message)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "S2ok2dn4fQci"}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "6m0Z-2xJfQZe"}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "F0IeT_RBfQS-"}, "outputs": [], "source": []}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}